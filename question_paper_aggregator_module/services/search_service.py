"""
Search Service for Question Paper Aggregator
Contains the main business logic for searching and verifying papers
Integrated with PYQs Admin upload functionality
"""

import os
import datetime
import logging
import tempfile
from typing import List, Dict, Any, Optional

from ..core import (
    find_question_paper, 
    download_pdf, 
    find_question_paper_without_year,
    check_with_gpt_vision, 
    extract_first_page_as_image
)
from .notification_service import NotificationService
from utils.pyqs_utils import upload_question_paper_to_s3
from db_config.pyqs_admin_db import create_exam_document

logger = logging.getLogger(__name__)


class SearchResult:
    """Data class for search results"""
    
    def __init__(self, subject: str, year: str):
        self.subject = subject
        self.year = year
        self.status = 'searching'
        self.message = f"Searching for {subject} paper ({year})..."
        self.papers = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'subject': self.subject,
            'year': self.year,
            'status': self.status,
            'message': self.message,
            'papers': self.papers
        }


class SearchService:
    """Service for orchestrating the paper search process"""
    
    def __init__(self, notification_service: NotificationService):
        self.notification_service = notification_service
    
    def search_papers(self, session_id: str, exam_id: int, exam_name: str, subject: str, 
                     num_years: int, language: Optional[str] = None, username: str = "system") -> List[Dict[str, Any]]:
        """
        Main entry point for paper search process integrated with PYQs Admin
        
        Args:
            session_id: Unique session identifier
            exam_id: ID of the exam in the database
            exam_name: Name of the exam
            subject: Subject to search for
            num_years: Number of years to search backwards
            language: Optional language filter
            username: Username for database operations
            
        Returns:
            List of search results
        """
        start_time = datetime.datetime.now()
        current_year = datetime.datetime.now().year
        
        # Log the start of the agentic process
        self._log_process_start(session_id, exam_name, subject, num_years, language, start_time)
        
        # Send initial notification
        self.notification_service.send_process_started(session_id, exam_name, subject, num_years, language)
        
        # Search papers for each year
        all_results = []
        uploaded_count = 0
        
        for i in range(num_years):
            year = str(current_year - 1 - i)
            year_results = self._search_year(session_id, exam_id, exam_name, subject, year, language, username)
            all_results.extend(year_results)
            
            # Count successful uploads
            for result in year_results:
                if result.get('status') == 'uploaded':
                    uploaded_count += 1
        
        # Check if fallback search is needed
        if uploaded_count == 0:
            fallback_result = self._perform_fallback_search(session_id, exam_id, exam_name, subject, language, username)
            if fallback_result:
                all_results.append(fallback_result)
                if fallback_result.get('status') == 'uploaded':
                    uploaded_count += 1
        
        # Sort and finalize results
        all_results.sort(key=lambda x: (x.get('year', '0'), x.get('subject', '')), reverse=True)
        
        # Log completion and send final notification
        self._log_process_completion(session_id, exam_name, start_time, all_results, uploaded_count)
        self.notification_service.send_process_complete(session_id, all_results, uploaded_count)
        
        return all_results
    
    def _search_year(self, session_id: str, exam_id: int, exam_name: str, subject: str, 
                    year: str, language: Optional[str], username: str) -> List[Dict[str, Any]]:
        """
        Search for papers in a specific year
        
        Args:
            session_id: Session identifier
            exam_id: ID of the exam in the database
            exam_name: Name of the exam
            subject: Subject to search for
            year: Year to search
            language: Optional language filter
            username: Username for database operations
            
        Returns:
            List of results for this year
        """
        logger.info(f"📅 PROCESSING YEAR {year} - {exam_name} - Session: {session_id}")
        self.notification_service.send_year_processing(session_id, year)
        
        try:
            result = SearchResult(subject, year)
            self.notification_service.send_subject_searching(session_id, year, subject)
            
            # Search for papers with enhanced logic (try first 5, then next 5 if needed)
            verified_paper_path = self._search_and_verify_papers(session_id, exam_name, subject, year, language, result)
            
            if verified_paper_path:
                # Upload the verified paper to the database
                upload_success = self._upload_to_database(
                    session_id, exam_id, verified_paper_path, int(year), None, None, username
                )
                
                if upload_success:
                    result.status = 'uploaded'
                    result.message = f"Successfully uploaded verified paper for {subject} ({year})"
                else:
                    result.status = 'found_not_uploaded'
                    result.message = f"Found verified paper for {subject} ({year}) but failed to upload"
            else:
                result.status = 'not_verified'
                result.message = f"No verified papers found for {subject} ({year})"
                self.notification_service.send_subject_complete(session_id, year, subject, 'not_verified')
            
            # Send year completion notification
            year_results = [result.to_dict()]
            self.notification_service.send_year_complete(session_id, year, len(year_results))
            
            return year_results
            
        except Exception as e:
            error_msg = f"Error processing year {year}: {e}"
            logger.critical(f"❌ ERROR - {error_msg} - Session: {session_id}")
            logger.critical(f"Request parameters: session_id={session_id}, year={year}")
            import traceback
            logger.critical(traceback.format_exc())
            self.notification_service.send_error(session_id, year, error_msg)
            return []

    def _search_and_verify_papers(self, session_id: str, exam_name: str, subject: str, year: str,
                                 language: Optional[str], result: SearchResult) -> Optional[str]:
        """
        Enhanced search logic: Try first 5 results, then next 5 if no verified papers found

        Args:
            session_id: Session identifier
            exam_name: Name of the exam
            subject: Subject name
            year: Year of the paper
            language: Optional language filter
            result: SearchResult object to update

        Returns:
            Path to verified paper if found, None otherwise
        """
        # Try first batch (results 1-5)
        pdf_links_batch1 = find_question_paper(exam_name, year, subject, language, start_index=0, num_results=5)

        if not isinstance(pdf_links_batch1, list) or len(pdf_links_batch1) == 0:
            result.status = 'not_found'
            result.message = f"No question papers found for {subject} ({year}) in {language if language else 'any language'}."
            self.notification_service.send_not_found(session_id, year, subject)
            return None

        # Process first batch
        self.notification_service.send_found_links(session_id, year, subject, len(pdf_links_batch1))
        logger.info(f"🔍 BATCH 1 - Processing {len(pdf_links_batch1)} PDFs from first 5 search results - Session: {session_id}")

        verified_paper_path = self._process_papers(session_id, exam_name, subject, year, language, pdf_links_batch1, result, batch_num=1)

        if verified_paper_path:
            return verified_paper_path

        # If no verified paper found in first batch, try second batch (results 6-10)
        logger.info(f"🔄 BATCH 2 - No verified papers in first batch, trying next 5 results - Session: {session_id}")

        self.notification_service.send_update(session_id, {
            'status': 'searching_next_batch',
            'year': year,
            'subject': subject,
            'message': f"No verified papers in first 5 results. Searching next 5 results for {subject} ({year})..."
        })

        pdf_links_batch2 = find_question_paper(exam_name, year, subject, language, start_index=5, num_results=5)

        if isinstance(pdf_links_batch2, list) and len(pdf_links_batch2) > 0:
            self.notification_service.send_update(session_id, {
                'status': 'found_links_batch2',
                'year': year,
                'subject': subject,
                'count': len(pdf_links_batch2),
                'message': f"Found {len(pdf_links_batch2)} additional potential papers for {subject} ({year})"
            })

            logger.info(f"🔍 BATCH 2 - Processing {len(pdf_links_batch2)} PDFs from next 5 search results - Session: {session_id}")
            verified_paper_path = self._process_papers(session_id, exam_name, subject, year, language, pdf_links_batch2, result, batch_num=2)

            if verified_paper_path:
                return verified_paper_path
        else:
            logger.info(f"🔍 BATCH 2 - No additional PDFs found in next 5 search results - Session: {session_id}")

        # No verified papers found in either batch
        logger.info(f"❌ NO VERIFIED PAPERS - Checked up to 10 search results for {subject} ({year}) - Session: {session_id}")
        return None

    def _process_papers(self, session_id: str, exam_name: str, subject: str, year: str, language: Optional[str],
                       pdf_links: List[str], result: SearchResult, batch_num: int = 1) -> Optional[str]:
        """
        Process and verify downloaded papers

        Args:
            session_id: Session identifier
            exam_name: Name of the exam
            subject: Subject name
            year: Year of the paper
            language: Optional language filter
            pdf_links: List of PDF URLs to process
            result: SearchResult object to update
            batch_num: Batch number for logging (1 or 2)

        Returns:
            Path to verified paper if found, None otherwise
        """
        for idx, pdf_url in enumerate(pdf_links):
            if not isinstance(pdf_url, str) or not pdf_url.startswith("http"):
                continue

            # Enhanced notification with batch information
            self.notification_service.send_update(session_id, {
                'status': 'downloading',
                'year': year,
                'subject': subject,
                'url': pdf_url,
                'batch': batch_num,
                'index': idx + 1,
                'total': len(pdf_links),
                'message': f"Downloading paper {idx + 1}/{len(pdf_links)} from batch {batch_num} for {subject} ({year})"
            })

            # Create temporary file for download
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                temp_path = temp_file.name

            downloaded_pdf = download_pdf(pdf_url, temp_path)

            if downloaded_pdf:
                self.notification_service.send_downloaded(session_id, year, subject, temp_path)

                # Verify paper
                verified_path = self._verify_paper(session_id, exam_name, subject, year, downloaded_pdf, result)
                if verified_path:
                    return verified_path
                else:
                    self.notification_service.send_not_verified(session_id, year, subject)
                    # Clean up temporary file
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        return None

    def _verify_paper(self, session_id: str, exam_name: str, subject: str, year: str,
                     downloaded_pdf: str, result: SearchResult) -> Optional[str]:
        """
        Verify a downloaded paper using AI

        Args:
            session_id: Session identifier
            exam_name: Name of the exam
            subject: Subject name
            year: Year of the paper
            downloaded_pdf: Path to downloaded PDF
            result: SearchResult object to update

        Returns:
            Path to verified paper if valid, None otherwise
        """
        self.notification_service.send_verifying(session_id, year, subject)

        image_path = extract_first_page_as_image(downloaded_pdf)

        if check_with_gpt_vision(image_path, exam_name, year, subject):
            paper_info = {
                'url': '',  # URL would be stored here
                'file_path': downloaded_pdf,
                'verified': True
            }
            result.papers.append(paper_info)

            # Log successful verification
            logger.info(f"✅ PAPER VERIFIED - {exam_name} {year} {subject} - Session: {session_id}")

            # Send verification success notification
            self.notification_service.send_verified(session_id, year, subject, downloaded_pdf)

            # Clean up image file
            try:
                if image_path and os.path.exists(image_path):
                    os.unlink(image_path)
            except:
                pass

            return downloaded_pdf

        # Clean up image file
        try:
            if image_path and os.path.exists(image_path):
                os.unlink(image_path)
        except:
            pass

        return None

    def _upload_to_database(self, session_id: str, exam_id: int, file_path: str,
                           year: int, month: Optional[str], shift: Optional[str], username: str) -> bool:
        """
        Upload verified paper to the database using existing PYQs Admin functionality

        Args:
            session_id: Session identifier
            exam_id: ID of the exam
            file_path: Path to the verified PDF file
            year: Year of the paper
            month: Optional month
            shift: Optional shift
            username: Username for database operations

        Returns:
            True if upload successful, False otherwise
        """
        try:
            self.notification_service.send_upload_started(session_id, file_path, year, month, shift)

            # Read the file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Upload to S3 using existing utility
            success, s3_path = upload_question_paper_to_s3(
                file_content,
                exam_id,
                year,
                month,
                shift
            )

            if not success:
                error_msg = "Failed to upload file to S3"
                logger.error(f"❌ UPLOAD ERROR - {error_msg} - Session: {session_id}")
                self.notification_service.send_upload_error(session_id, file_path, error_msg)
                return False

            # Create document record in database
            document_data = {
                "year": year,
                "month": month,
                "shift": shift,
                "question_paper_path": s3_path
            }

            document_id = create_exam_document(exam_id, document_data, username)

            if document_id:
                logger.info(f"✅ UPLOAD SUCCESS - Document ID: {document_id} - Session: {session_id}")
                self.notification_service.send_upload_success(session_id, file_path, year)

                # Clean up temporary file
                try:
                    os.unlink(file_path)
                except:
                    pass

                return True
            else:
                error_msg = "Failed to create document record in database"
                logger.error(f"❌ DATABASE ERROR - {error_msg} - Session: {session_id}")
                self.notification_service.send_upload_error(session_id, file_path, error_msg)
                return False

        except Exception as e:
            error_msg = f"Error uploading to database: {e}"
            logger.error(f"❌ UPLOAD ERROR - {error_msg} - Session: {session_id}")
            self.notification_service.send_upload_error(session_id, file_path, error_msg)
            return False

    def _perform_fallback_search(self, session_id: str, exam_id: int, exam_name: str,
                                subject: str, language: Optional[str], username: str) -> Optional[Dict[str, Any]]:
        """
        Perform fallback search without year when no papers are found

        Args:
            session_id: Session identifier
            exam_id: ID of the exam in the database
            exam_name: Name of the exam
            subject: Subject to search for
            language: Optional language filter
            username: Username for database operations

        Returns:
            Fallback search result or None
        """
        logger.info(f"🔄 FALLBACK SEARCH - No verified papers found, trying search without year - Session: {session_id}")
        self.notification_service.send_fallback_search(session_id, exam_name, subject)

        try:
            # Search without year
            pdf_links = find_question_paper_without_year(exam_name, subject, language)

            if pdf_links and len(pdf_links) > 0:
                self.notification_service.send_fallback_found_links(session_id, subject, len(pdf_links))

                # Try to verify papers from fallback search
                fallback_result = SearchResult(subject, 'Unknown')

                verified_paper_path = self._process_fallback_papers(session_id, exam_name, subject, language, pdf_links, fallback_result)

                if verified_paper_path:
                    # Upload the verified paper to the database (use current year as fallback)
                    current_year = datetime.datetime.now().year
                    upload_success = self._upload_to_database(
                        session_id, exam_id, verified_paper_path, current_year, None, "Fallback Search", username
                    )

                    if upload_success:
                        fallback_result.status = 'uploaded'
                        fallback_result.message = f"Successfully uploaded fallback paper for {subject}"
                        logger.info(f"🎉 FALLBACK SEARCH SUCCESSFUL - Uploaded verified paper for {subject} - Session: {session_id}")
                    else:
                        fallback_result.status = 'found_not_uploaded'
                        fallback_result.message = f"Found fallback paper for {subject} but failed to upload"
                        logger.info(f"⚠️ FALLBACK SEARCH PARTIAL - Found but not uploaded for {subject} - Session: {session_id}")

                    return fallback_result.to_dict()
                else:
                    fallback_result.status = 'not_verified'
                    fallback_result.message = f"No verified fallback papers found for {subject}"
                    logger.info(f"❌ FALLBACK SEARCH FAILED - No verified papers found for {subject} - Session: {session_id}")
                    return fallback_result.to_dict()
            else:
                logger.info(f"❌ FALLBACK SEARCH FAILED - No PDFs found in fallback search for {subject} - Session: {session_id}")
                self.notification_service.send_fallback_failed(session_id, subject)

        except Exception as e:
            error_msg = f"Error in fallback search: {e}"
            logger.error(f"❌ FALLBACK SEARCH ERROR - {error_msg} - Session: {session_id}")
            self.notification_service.send_fallback_error(session_id, error_msg)

        return None

    def _process_fallback_papers(self, session_id: str, exam_name: str, subject: str, language: Optional[str],
                                pdf_links: List[str], result: SearchResult) -> Optional[str]:
        """
        Process papers from fallback search

        Args:
            session_id: Session identifier
            exam_name: Name of the exam
            subject: Subject name
            language: Optional language filter
            pdf_links: List of PDF URLs to process
            result: SearchResult object to update

        Returns:
            Path to verified paper if found, None otherwise
        """
        for idx, pdf_url in enumerate(pdf_links):
            if not isinstance(pdf_url, str) or not pdf_url.startswith("http"):
                continue

            self.notification_service.send_fallback_downloading(session_id, subject, pdf_url, idx + 1, len(pdf_links))

            # Create temporary file for download
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                temp_path = temp_file.name

            downloaded_pdf = download_pdf(pdf_url, temp_path)

            if downloaded_pdf:
                self.notification_service.send_fallback_downloaded(session_id, subject, temp_path)

                verified_path = self._verify_fallback_paper(session_id, exam_name, subject, downloaded_pdf, result)
                if verified_path:
                    return verified_path
                else:
                    self.notification_service.send_fallback_not_verified(session_id, subject)
                    # Clean up temporary file
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        return None

    def _verify_fallback_paper(self, session_id: str, exam_name: str, subject: str,
                              downloaded_pdf: str, result: SearchResult) -> Optional[str]:
        """
        Verify a fallback paper using AI

        Args:
            session_id: Session identifier
            exam_name: Name of the exam
            subject: Subject name
            downloaded_pdf: Path to downloaded PDF
            result: SearchResult object to update

        Returns:
            Path to verified paper if valid, None otherwise
        """
        self.notification_service.send_fallback_verifying(session_id, subject)

        image_path = extract_first_page_as_image(downloaded_pdf)

        # For fallback, we check if it's relevant to the exam and subject (without specific year)
        if check_with_gpt_vision(image_path, exam_name, "any year", subject):
            paper_info = {
                'url': '',  # URL would be stored here
                'file_path': downloaded_pdf,
                'verified': True
            }
            result.papers.append(paper_info)

            # Log successful fallback verification
            logger.info(f"✅ FALLBACK PAPER VERIFIED - {exam_name} {subject} - Session: {session_id}")

            # Send verification success notification
            self.notification_service.send_fallback_verified(session_id, subject, downloaded_pdf)

            # Clean up image file
            try:
                if image_path and os.path.exists(image_path):
                    os.unlink(image_path)
            except:
                pass

            return downloaded_pdf

        # Clean up image file
        try:
            if image_path and os.path.exists(image_path):
                os.unlink(image_path)
        except:
            pass

        return None

    def _log_process_start(self, session_id: str, exam_name: str, subject: str, num_years: int,
                          language: Optional[str], start_time: datetime.datetime) -> None:
        """Log the start of the agentic process"""
        current_year = datetime.datetime.now().year
        logger.info(f"🤖 AGENTIC PROCESS STARTED - Session: {session_id}")
        logger.info(f"   📋 Exam: {exam_name}")
        logger.info(f"   📚 Subject: {subject}")
        logger.info(f"   📅 Years: {num_years} (from {current_year} backwards)")
        logger.info(f"   🌐 Language: {language if language else 'Any'}")
        logger.info(f"   ⏰ Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    def _log_process_completion(self, session_id: str, exam_name: str, start_time: datetime.datetime,
                               all_results: List[Dict[str, Any]], uploaded_count: int) -> None:
        """Log the completion of the agentic process"""
        end_time = datetime.datetime.now()
        duration = end_time - start_time

        # Count successful results
        total_subjects = len(all_results)

        # Log the completion of the agentic process
        logger.info(f"🎉 AGENTIC PROCESS COMPLETED - Session: {session_id}")
        logger.info(f"   ⏱️  Duration: {duration.total_seconds():.2f} seconds")
        logger.info(f"   📊 Results: {uploaded_count}/{total_subjects} papers uploaded successfully")
        logger.info(f"   ✅ Success Rate: {(uploaded_count/total_subjects*100):.1f}%" if total_subjects > 0 else "   ✅ Success Rate: 0%")
        logger.info(f"   🏁 End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"   📋 Summary: {exam_name} search and upload completed successfully")
