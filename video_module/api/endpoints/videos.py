"""
API endpoints for video generation.
"""

import os
import time
import uuid
import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, Request
from fastapi.responses import FileResponse
from auth.dependencies import require_login
from auth.rbac import require_roles

from video_module.core.video_config import settings
from video_module.models.video import VideoResponse
from video_module.services.video_generator import video_generator

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.post("/generate", response_model=VideoResponse, status_code=status.HTTP_200_OK)
async def generate_video(
    request: Request,
    title: str = Form("Question Paper"),
    bg_color: str = Form("#FFFFFF"),
    title_color: str = Form("#000000"),
    text_color: str = Form("#000000"),
    duration: int = Form(60),
    aspect_ratios: List[str] = Form([]),
    html_file: Optional[UploadFile] = File(None),
    html_content: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Generate video from HTML content.

    Args:
        title: Video title
        bg_color: Background color
        title_color: Title color
        text_color: Text color
        duration: Video duration in seconds
        aspect_ratios: List of aspect ratios
        html_file: HTML file upload
        html_content: HTML content as text

    Returns:
        JSON with video URLs
    """
    try:
        # Validate duration range
        if duration < 10:
            duration = 10
        elif duration > 300:
            duration = 300

        # Set default aspect ratio if none selected
        if not aspect_ratios:
            aspect_ratios = ['16:9']

        # Get HTML content (either from file or text area)
        content = ""
        if html_file and html_file.filename:
            # Save the file temporarily
            file_path = os.path.join(settings.UPLOAD_DIR, f"{uuid.uuid4()}.html")
            with open(file_path, 'wb') as f:
                content_bytes = await html_file.read()
                f.write(content_bytes)

            # Read the file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Clean up
            os.remove(file_path)
        else:
            content = html_content

        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No HTML content provided"
            )

        # Generate a unique filename
        output_filename = f"video_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        # Generate videos
        video_paths = video_generator.generate_videos(
            html_content=content,
            title=title,
            output_filename=output_filename,
            aspect_ratios=aspect_ratios,
            bg_color=bg_color,
            title_color=title_color,
            text_color=text_color,
            duration=duration
        )

        # Prepare response with video URLs
        response = {
            'success': True,
            'videos': {}
        }

        for ratio, path in video_paths.items():
            if path:
                # Convert to URL
                url = f"/video-static/output/{path}"
                response['videos'][ratio] = url

        return response

    except Exception as e:
        logger.critical(f"Error generating video: {e}")
        logger.critical(f"Request parameters: title={title}, bg_color={bg_color}, title_color={title_color}, text_color={text_color}, duration={duration}, aspect_ratios={aspect_ratios}, html_file={html_file.filename if html_file else 'None'}, html_content_length={len(html_content) if html_content else 0}")
        import traceback
        logger.critical(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/download/{filename}")
async def download_file(
    request: Request,
    filename: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Download a generated video file.

    Args:
        filename: Name of the video file

    Returns:
        Video file
    """
    file_path = os.path.join(settings.OUTPUT_DIR, filename)
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    return FileResponse(file_path, filename=filename)
